import { PrismaClient } from 'generated/prisma';
import * as bcrypt from 'bcrypt';
const prisma = new PrismaClient();

async function main() {
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      fullName: 'Admin',
      passwordHash: bcrypt.hashSync('admin123', 10),
      role: 'ADMIN',
      status: 'ACTIVE',
    },
  });

  const regularUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      fullName: 'User',
      passwordHash: bcrypt.hashSync('user123', 10),
      role: 'USER',
      status: 'ACTIVE',
    },
  });

  const inactiveUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      fullName: 'Inactive User',
      passwordHash: bcrypt.hashSync('inactive123', 10),
      role: 'USER',
      status: 'INACTIVE',
    },
  });

  const rejectedUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      fullName: 'Rejected User',
      passwordHash: bcrypt.hashSync('rejected123', 10),
      role: 'USER',
      status: 'REJECTED',
    },
  });

  console.log('Seeded users:');
  console.log(adminUser);
  console.log(regularUser);
  console.log(inactiveUser);
  console.log(rejectedUser);
}

main()
  .then(async () => {
    console.log('Seeding completed successfully.');
    await prisma.$disconnect();
  })
  .catch(async (error) => {
    console.error('Error seeding database:', error);
    await prisma.$disconnect();
    process.exit(1);
  });
