import {
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';

import { JwtService, JwtSignOptions } from '@nestjs/jwt';
import { PrismaService } from 'src/prisma.service';
import BasePayload from './interfaces/payload.interface';

@Injectable()
export class TokenService {
  private readonly logger = new Logger(TokenService.name);
  constructor(
    private jwtService: JwtService,
    private prismaService: PrismaService,
  ) {}

  async generateToken(payload: BasePayload, options?: JwtSignOptions) {
    try {
      const value = await this.jwtService.signAsync(payload, options);
      return {
        value,
        options: options || {},
      };
    } catch (error) {
      this.logger.error(`Token generation failed: ${error.message}`);
      throw new InternalServerErrorException('Failed to generate token');
    }
  }

  async validateToken(token: string) {
    try {
      const payload: BasePayload = await this.jwtService.verifyAsync(token);
      return {
        message: 'Token validated successfully',
        data: payload,
        statusCode: HttpStatus.OK,
      };
    } catch (error) {
      this.logger.error(`Token validation failed: ${error.message}`);
      return {
        message: 'Token validation failed',
        data: null,
        statusCode: HttpStatus.UNAUTHORIZED,
      };
    }
  }

  async saveRefreshToken({
    userId,
    refreshToken,
  }: {
    userId: string;
    refreshToken: string;
  }) {
    this.logger.log(`Saving refresh token for user ${userId}`);
    try {
      await this.prismaService.refreshToken.upsert({
        where: { userId },
        create: {
          userId,
          value: refreshToken,
        },
        update: {
          value: refreshToken,
        },
      });
      this.logger.log(`Refresh token saved successfully for user ${userId}`);
      return {
        message: 'Refresh token saved successfully',
        statusCode: HttpStatus.OK,
      };
    } catch (error) {
      this.logger.error(
        `Failed to save refresh token for user ${userId}: ${error.message}`,
      );
      throw new InternalServerErrorException('Failed to save refresh token');
    }
  }

  // Récupérer le jeton de rafraîchissement pour un utilisateur
  async getRefreshToken(userId: string) {
    this.logger.log(`Retrieving refresh token for user ${userId}`);
    try {
      const refreshToken = await this.prismaService.refreshToken.findUnique({
        where: { userId },
      });
      if (!refreshToken) {
        this.logger.warn(`No refresh token found for user ${userId}`);
        return {
          message: 'No refresh token found',
          data: null,
          statusCode: HttpStatus.NOT_FOUND,
        };
      }
      return {
        message: 'Refresh token retrieved successfully',
        data: {
          value: refreshToken.value,
        },
        statusCode: HttpStatus.OK,
      };
    } catch (error) {
      this.logger.error(
        `Failed to retrieve refresh token for user ${userId}: ${error.message}`,
      );
      throw new InternalServerErrorException(
        'Failed to retrieve refresh token',
      );
    }
  }

  // Supprimer le jeton de rafraîchissement, utile pour la déconnexion
  async deleteRefreshToken(userId: string) {
    this.logger.log(`Deleting refresh token for user ${userId}`);
    try {
      await this.prismaService.refreshToken.delete({
        where: { userId },
      });
      this.logger.log(`Refresh token deleted successfully for user ${userId}`);
      return {
        message: 'Refresh token deleted successfully',
        statusCode: HttpStatus.OK,
      };
    } catch (error) {
      this.logger.error(
        `Failed to delete refresh token for user ${userId}: ${error.message}`,
      );
      throw new InternalServerErrorException('Failed to delete refresh token');
    }
  }

  // Rafraîchir le jeton d'accès en utilisant le jeton de rafraîchissement
  async refreshAccessToken(userId: string) {
    try {
      this.logger.log(`Refreshing access token for user ${userId}`);

      const existingRefreshToken = await this.getRefreshToken(userId);

      if (!existingRefreshToken.data) {
        this.logger.warn(`No refresh token found for user ${userId}`);
        return {
          message: 'No refresh token found',
          data: null,
          statusCode: HttpStatus.NOT_FOUND,
        };
      }

      const payload = await this.validateToken(existingRefreshToken.data.value);

      if (!payload.data) {
        this.logger.warn('Invalid refresh token');
        return {
          message: 'Invalid refresh token',
          data: null,
          statusCode: HttpStatus.UNAUTHORIZED,
        };
      }

      const newAccessToken = await this.generateToken(payload.data);
      if (!newAccessToken) {
        this.logger.error('Failed to generate new access token');
        throw new InternalServerErrorException(
          'Failed to generate new access token',
        );
      }

      this.logger.log('Access token refreshed successfully');
      return {
        message: 'Access token refreshed successfully',
        data: newAccessToken,
        statusCode: HttpStatus.OK,
      };
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException('Failed to refresh access token');
    }
  }
}
