import {
  ConflictException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import { CreateUserDto } from './dto/create-user.dto';
import * as bcrypt from 'bcrypt';
import { UpdateUserDto } from './dto/update-user.dto';
import { MailerService } from 'src/mailer/mailer.service';
import {
  registerFromInvitation,
  registerTemplate,
} from 'src/mailer/templates/register.template';
@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);
  constructor(
    private prisma: PrismaService,
    private mailerService: MailerService,
  ) {}

  // Méthode pour trouver un utilisateur par son email
  async findOne(email: string) {
    try {
      const user = await this.prisma.user.findUnique({
        where: { email },
      });

      return user; // Return null if not found, don't throw error
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException('Failed to find user');
    }
  }

  // Méthode pour créer un nouvel utilisateur
  async createUser(createUserDto: CreateUserDto) {
    try {
      // Vérification de l'existence de l'utilisateur
      const existingUser = await this.prisma.user.findUnique({
        where: { email: createUserDto.email },
      });
      // Si l'utilisateur existe déjà, on lance une erreur
      if (existingUser) {
        throw new ConflictException(
          `User with email ${createUserDto.email} already exists`,
        );
      }
      // Hachage du mot de passe
      const hashedPassword = await bcrypt.hash(createUserDto.password, 10);
      // Création de l'utilisateur
      const user = await this.prisma.user.create({
        data: {
          email: createUserDto.email,
          fullName: createUserDto.fullName, // Utilisation de fullName au lieu de name
          passwordHash: hashedPassword, // Stockage du mot de passe haché
          role: createUserDto.role || 'USER', // Valeur par défaut pour le rôle
          status: createUserDto.status || 'INACTIVE', // Valeur par défaut pour le statut
        },
        // Sélection des champs à retourner
        select: {
          id: true,
          email: true,
          fullName: true,
          role: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          lastLogin: true,
        },
      });

      try {
        await this.mailerService.sendMail({
          to: user.email,
          subject: 'Welcome to PixiGenerator',
          template:
            user.status === 'INACTIVE'
              ? registerTemplate({
                  username: user.fullName as string,
                  verificationLink: `https://example.com/verify?token=${user.id}`,
                })
              : registerFromInvitation({
                  username: user.fullName as string,
                }),
        });
      } catch (error) {
        this.logger.error(`Failed to send welcome email: ${error.message}`);
      }

      this.logger.log(
        `User created successfully with email: ${createUserDto.email}`,
      );

      return {
        message: 'User created successfully',
        data: user,
        statusCode: HttpStatus.CREATED,
      };
    } catch (error) {
      this.logger.error(`Error creating user: ${error.message}`);
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to create user with email ${createUserDto.email}`,
      );
    }
  }

  // Méthode pour mettre à jour un utilisateur
  async updateUser(id: string, updateUserDto: UpdateUserDto) {
    try {
      // Vérification de l'existence de l'utilisateur
      const existingUser = await this.prisma.user.findUnique({
        where: { id },
      });
      // Si l'utilisateur n'existe pas, on lance une erreur
      if (!existingUser) {
        throw new NotFoundException(`User with id ${id} does not exist`);
      }
      // Mise à jour de l'utilisateur
      const updatedUser = await this.prisma.user.update({
        where: { id },
        data: {
          ...updateUserDto,
          passwordHash: updateUserDto.password
            ? await bcrypt.hash(updateUserDto.password, 10)
            : undefined, // Hachage du mot de passe si fourni
        },
        select: {
          id: true,
          email: true,
          fullName: true,
          role: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          lastLogin: true,
        },
      });

      return {
        message: 'User updated successfully',
        data: updatedUser,
        statusCode: HttpStatus.OK,
      };
    } catch (error) {
      this.logger.error(error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to update user with id ${id}`,
      );
    }
  }
}
