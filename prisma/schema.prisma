// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  USER
  ADMIN
}

enum UserStatus {
  ACTIVE
  INACTIVE
  REJECTED
}

enum InvitationStatus {
  PENDING
  ACCEPTED
  REJECTED
}

enum ProjectStatus {
  ACTIVE
  INACTIVE
  ARCHIVED
}

enum GenerationStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
}

enum GenerationType {
  UI
  DOCUMENTATION
}

model User {
  id           String        @id @default(cuid())
  email        String        @unique
  fullName     String?
  passwordHash String
  status       UserStatus    @default(INACTIVE)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  lastLogin    DateTime?
  role         UserRole      @default(USER)
  refreshToken RefreshToken?
  invitations  Invitation[]  @relation("InviterInvitations")
  invitation   Invitation?   @relation("UserInvitations", fields: [invitationId], references: [id])
  invitationId String?       @unique

  // Project relationships
  projectMemberships ProjectMember[]
  createdProjects    Project[]       @relation("ProjectCreator")
  generations        Generation[]    @relation("GenerationCreator")

  @@map("users")
}

model RefreshToken {
  id        String   @id @default(cuid())
  userId    String   @unique
  value     String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id])

  @@map("refresh_tokens")
}

model Invitation {
  id          String           @id @default(cuid())
  email       String           @unique
  token       String           @unique
  status      InvitationStatus @default(PENDING)
  userId      String?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  invitedBy   User?            @relation("InviterInvitations", fields: [userId], references: [id])
  invitedById String?
  invitee     User?            @relation("UserInvitations")

  @@map("invitations")
}

model Project {
  id          String        @id @default(cuid())
  name        String
  description String?
  status      ProjectStatus @default(ACTIVE)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relationships
  createdById String
  createdBy   User            @relation("ProjectCreator", fields: [createdById], references: [id])
  members     ProjectMember[]
  generations Generation[]

  @@map("projects")
}

model ProjectMember {
  id        String   @id @default(cuid())
  projectId String
  userId    String
  joinedAt  DateTime @default(now())

  // Relationships
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([projectId, userId])
  @@map("project_members")
}

model Generation {
  id           String           @id @default(cuid())
  type         GenerationType
  prompt       String
  status       GenerationStatus @default(PENDING)
  result       String? // Generated content (UI code, documentation, etc.)
  metadata     Json? // Additional metadata like file paths, settings, etc.
  errorMessage String? // Error details if generation failed
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt

  // Relationships
  projectId   String
  createdById String
  project     Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  createdBy   User    @relation("GenerationCreator", fields: [createdById], references: [id])

  @@map("generations")
}
